# Side Responsive TOC by Safeoid

A lightweight, responsive Table of Contents plugin for WordPress with floating sidebar on desktop and sticky header on mobile. Includes Elementor integration and SEO schema markup.

## 🧩 Plugin Overview

- **Plugin Name**: Side Responsive TOC by Safeoid
- **Plugin URI**: https://safeoid.com/floating-toc
- **Version**: 1.0.0
- **Author**: Safeoid
- **License**: GPLv2 or later
- **Text Domain**: safeoid-toc
- **Compatibility**: WordPress 6.5+, Elementor, Rank Math, caching plugins

## 🎯 Primary Features

### ✅ Frontend Table of Contents (TOC)
- Auto-generates TOC based on selected headings (H1–H6)
- Floating sidebar on desktop
- Sticky header TOC on mobile, collapsible via toggle
- Fully responsive and styled with CSS
- No jQuery; lightweight Vanilla JS and CSS only
- No database writes — dynamically renders TOC per page
- Adds id attributes to headings automatically (if missing)

### ⚙️ Admin Settings Panel
- Accessible from WordPress Sidebar (NOT inside Settings)
- Options include:
  - Select which headings to include (h1, h2, h3, ... h6)
  - Customize TOC label (e.g., "Table of Contents")
  - Enable/disable floating TOC on desktop
  - Enable/disable sticky top TOC on mobile
  - Customize colors (text/link/background)
  - Enable or disable Rank Math compatible schema output

### 🔌 Elementor Integration
- A custom Elementor widget: **Floating TOC**
- Can be dragged onto any page
- Renders a dynamic TOC based on page content
- Inherits global settings or uses custom widget settings

### 🔍 SEO & Schema Markup
- Adds TableOfContents schema.org JSON-LD in the header
- Fully compatible with Rank Math and other SEO plugins
- Schema only loads when TOC is rendered
- Automatically parses headings and outputs hierarchical data

### 🧹 Clean Activation & Deactivation
- No data saved in wp_options or other tables unless user manually sets preferences
- On deactivation: clears hooks and stops outputting TOC
- On deletion: removes settings (if any) and files cleanly using uninstall.php

## 📁 File Structure

```
safeoid-toc/
├── safeoid-toc.php              # Main plugin file
├── css/
│   └── style.css                # Responsive TOC styles
├── js/
│   └── toc.js                   # Toggle functionality
├── inc/
│   ├── toc-output.php          # Generates the TOC HTML
│   ├── settings-page.php       # Admin UI
│   └── schema.php              # Outputs JSON-LD schema
├── widgets/
│   └── elementor-toc-widget.php # Elementor integration
├── uninstall.php               # Clean removal
└── README.md                   # This file
```

## 🖼️ Sample HTML Output (Frontend TOC)

```html
<div class="safeoid-toc desktop">
  <h3 class="toc-heading">Table of Contents</h3>
  <div class="toc-content">
    <ul>
      <li><a href="#intro">Introduction</a></li>
      <li><a href="#features">Features</a></li>
      <li><a href="#faq">FAQs</a></li>
    </ul>
  </div>
</div>

<div class="safeoid-toc mobile">
  <div class="toc-toggle">☰ Table of Contents</div>
  <div class="toc-content">
    <ul>
      <li><a href="#intro">Introduction</a></li>
      <li><a href="#features">Features</a></li>
      <li><a href="#faq">FAQs</a></li>
    </ul>
  </div>
</div>
```

## 🖌️ Styling & Responsive Behavior

### On desktop:
- Floats left (position: fixed)
- Pushes main content to the right
- Full height sidebar with scroll

### On mobile:
- Full-width sticky TOC at top
- Hidden content, shown on toggle
- Collapsible with smooth animation

### Uses only pure CSS and vanilla JS

## 🧠 Smart Behavior

- Adds id to each heading (based on slug of text)
- Prevents duplicate ids
- No extra requests or dependencies
- Works with caching plugins, Elementor, and Rank Math
- Smooth scrolling to sections
- Responsive design that adapts to screen size

## 🔐 Security & Performance

- Escapes all output with `esc_html()` and `esc_attr()`
- No AJAX or DB writes = zero risk of query bloat
- Uses `plugin_dir_path()` and `plugin_dir_url()` for reliable asset loading
- Lightweight - only loads on posts and pages
- No jQuery dependency

## 📦 Installation

1. Upload the `safeoid-toc` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'Safeoid TOC' in the WordPress admin sidebar to configure settings
4. The TOC will automatically appear on posts and pages with headings

## ⚙️ Configuration

### Admin Settings
Navigate to **Safeoid TOC** in your WordPress admin sidebar to configure:

- **Include Headings**: Choose which heading levels (H1-H6) to include
- **TOC Label**: Customize the heading text
- **Display Options**: Enable/disable desktop and mobile versions
- **Colors**: Customize text, link, and background colors
- **SEO Options**: Enable/disable schema markup

### Elementor Widget
1. Edit any page with Elementor
2. Search for "Floating TOC" widget
3. Drag it to your desired location
4. Configure widget-specific settings or use global settings

## 🔧 Customization

### CSS Customization
The plugin uses CSS custom properties for easy theming:

```css
:root {
  --safeoid-toc-text-color: #333333;
  --safeoid-toc-link-color: #0073aa;
  --safeoid-toc-bg-color: #ffffff;
}
```

### Hooks and Filters
The plugin is built with WordPress best practices and includes hooks for developers.

## 🐛 Troubleshooting

### TOC not appearing?
- Check that you have enabled either desktop or mobile display
- Ensure your content has the selected heading levels
- Verify the plugin is activated

### Conflicts with other plugins?
- Try disabling schema markup in settings
- Check for CSS conflicts in browser developer tools
- Ensure no other TOC plugins are active

### Mobile TOC not working?
- Check that JavaScript is enabled
- Verify no JavaScript errors in browser console
- Ensure mobile display is enabled in settings

## 📞 Support

For support, feature requests, or bug reports, please visit:
- **Plugin URI**: https://safeoid.com/floating-toc
- **Author**: Safeoid

## 📄 License

This plugin is licensed under the GPLv2 or later license.

## 🔄 Changelog

### 1.0.0
- Initial release
- Responsive TOC with desktop and mobile layouts
- Elementor widget integration
- SEO schema markup
- Admin settings panel
- No jQuery dependency

 /**
 * Safeoid TOC Styles
 * Fully Floating Table of Contents without taking layout space
 */

.floating-toc {
    position: fixed;
    top: 60px;
    left: 20px;
    width: 250px;
    background: var(--safeoid-toc-bg-color, #fff);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    z-index: 9999;
    font-family: Arial, sans-serif;
    color: var(--safeoid-toc-text-color, #111);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
}

/* Collapsed state */
.floating-toc.collapsed {
    width: auto;
    min-width: 200px;
    padding: 10px 15px;
    cursor: pointer;
}

.floating-toc.collapsed .toc-content {
    display: none;
}

.floating-toc.collapsed .toc-heading {
    display: none;
}

.floating-toc .toc-heading {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--safeoid-toc-text-color, #111);
}

.floating-toc .toc-toggle {
    display: none;
    font-size: 14px;
    font-weight: bold;
    color: var(--safeoid-toc-text-color, #111);
    cursor: pointer;
    user-select: none;
}

.floating-toc .toc-toggle::after {
    content: " ▼";
    font-size: 12px;
    margin-left: 5px;
}

.floating-toc.collapsed .toc-toggle {
    display: block;
}

.floating-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.floating-toc ul li {
    margin: 8px 0;
}

.floating-toc ul li a {
    text-decoration: none;
    color: var(--safeoid-toc-link-color, #0073e6);
    font-size: 14px;
}

.floating-toc ul li a:hover {
    text-decoration: underline;
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

@media (max-width: 1024px) {
    .floating-toc {
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        border: none;
        padding: 12px 16px;
    }

    .floating-toc .toc-heading {
        display: none;
    }

    .floating-toc .toc-toggle {
        display: block;
        font-weight: bold;
        font-size: 16px;
        cursor: pointer;
        color: var(--safeoid-toc-text-color, #111);
    }

    .floating-toc .toc-content {
        display: none;
        margin-top: 10px;
    }

    body.toc-open .floating-toc .toc-content {
        display: block;
    }
}

/* Customizable colors via settings */
.floating-toc {
    color: var(--safeoid-toc-text-color, #111);
    background-color: var(--safeoid-toc-bg-color, #ffffff);
}

.floating-toc a {
    color: var(--safeoid-toc-link-color, #0073e6);
}

.floating-toc a:hover {
    color: var(--safeoid-toc-link-hover-color, #005bb5);
}

/* Elementor widget styles */
.elementor-widget-safeoid-toc .floating-toc,
.floating-toc.elementor-widget {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    height: auto !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    max-height: none !important;
}

.elementor-widget-safeoid-toc .floating-toc .toc-toggle,
.floating-toc.elementor-widget .toc-toggle {
    display: none !important;
}

.elementor-widget-safeoid-toc .floating-toc .toc-heading,
.floating-toc.elementor-widget .toc-heading {
    display: block !important;
}

.elementor-widget-safeoid-toc .floating-toc .toc-content,
.floating-toc.elementor-widget .toc-content {
    display: block !important;
}

/* Print styles */
@media print {
    .floating-toc {
        display: none !important;
    }
}

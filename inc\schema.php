<?php
/**
 * Schema Markup Class
 * Outputs TableOfContents JSON-LD schema for SEO
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOCSchema {
    
    private $headings = array();
    
    public function __construct() {
        // Get headings from TOC output class
        include_once SAFEOID_TOC_PLUGIN_DIR . 'inc/toc-output.php';
        $toc_output = new SafeoidTOCOutput();
        $this->headings = $toc_output->get_headings();
    }
    
    /**
     * Output the schema markup
     */
    public function output() {
        if (empty($this->headings)) {
            return;
        }
        
        $schema = $this->generate_schema();
        
        if (!empty($schema)) {
            echo '<script type="application/ld+json">';
            echo wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            echo '</script>';
        }
    }
    
    /**
     * Generate the schema array
     */
    private function generate_schema() {
        global $post;
        
        if (!$post || empty($this->headings)) {
            return array();
        }
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title($post->ID),
            'url' => get_permalink($post->ID),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author)
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            )
        );
        
        // Add table of contents
        $toc_items = $this->build_toc_items();
        if (!empty($toc_items)) {
            $schema['tableOfContents'] = array(
                '@type' => 'TableOfContents',
                'tocEntry' => $toc_items
            );
        }
        
        return $schema;
    }
    
    /**
     * Build TOC items for schema
     */
    private function build_toc_items() {
        $items = array();
        $current_url = get_permalink();
        
        foreach ($this->headings as $heading) {
            $items[] = array(
                '@type' => 'TocEntry',
                'name' => $heading['text'],
                'url' => $current_url . '#' . $heading['id'],
                'position' => count($items) + 1
            );
        }
        
        return $items;
    }
    
    /**
     * Check if schema should be output
     */
    public function should_output_schema() {
        // Don't output on admin pages
        if (is_admin()) {
            return false;
        }
        
        // Only output on posts and pages
        if (!is_single() && !is_page()) {
            return false;
        }
        
        // Check if schema is disabled in settings
        $settings = get_option('safeoid_toc_settings', array());
        if (isset($settings['disable_schema']) && $settings['disable_schema']) {
            return false;
        }
        
        // Check if we have headings
        if (empty($this->headings)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get structured data for testing/debugging
     */
    public function get_schema_data() {
        return $this->generate_schema();
    }
}

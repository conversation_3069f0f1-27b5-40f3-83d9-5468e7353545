<?php
/**
 * Settings Page
 * Admin interface for configuring the TOC plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings using the new option structure
$headings = (array) get_option('safeoid_toc_headings', array('h2', 'h3', 'h4'));
$post_types = (array) get_option('safeoid_toc_post_types', array('post', 'page'));
$auto_insert = get_option('safeoid_toc_auto_insert', true);
$toc_label = get_option('safeoid_toc_toc_label', __('Table of Contents', SAFEOID_TOC_TEXT_DOMAIN));
$enable_desktop = get_option('safeoid_toc_enable_desktop', true);
$enable_mobile = get_option('safeoid_toc_enable_mobile', true);
$text_color = get_option('safeoid_toc_text_color', '#111');
$link_color = get_option('safeoid_toc_link_color', '#0073e6');
$background_color = get_option('safeoid_toc_background_color', '#ffffff');
$disable_schema = get_option('safeoid_toc_disable_schema', false);
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <form method="post" action="options.php">
        <?php
        settings_fields('safeoid_toc_settings');
        do_settings_sections('safeoid_toc_settings');
        ?>

        <table class="form-table">
            <tr valign="top">
                <th scope="row"><?php esc_html_e('Post Types to Display TOC', SAFEOID_TOC_TEXT_DOMAIN); ?></th>
                <td>
                    <?php foreach (get_post_types(array('public' => true), 'objects') as $post_type): ?>
                        <label>
                            <input type="checkbox" name="safeoid_toc_post_types[]"
                                value="<?php echo esc_attr($post_type->name); ?>"
                                <?php checked(in_array($post_type->name, $post_types)); ?>>
                            <?php echo esc_html($post_type->label); ?>
                        </label><br>
                    <?php endforeach; ?>
                </td>
            </tr>
            <tr valign="top">
                <th scope="row"><?php esc_html_e('Auto Insert TOC', SAFEOID_TOC_TEXT_DOMAIN); ?></th>
                <td>
                    <label>
                        <input type="checkbox" name="safeoid_toc_auto_insert"
                            value="1" <?php checked($auto_insert, 1); ?>>
                        <?php esc_html_e('Enable auto insertion before content', SAFEOID_TOC_TEXT_DOMAIN); ?>
                    </label>
                </td>
            </tr>
            <tr valign="top">
                <th scope="row"><?php esc_html_e('Headings to include', SAFEOID_TOC_TEXT_DOMAIN); ?></th>
                <td>
                    <?php foreach (array('h1', 'h2', 'h3', 'h4', 'h5', 'h6') as $h): ?>
                        <label>
                            <input type="checkbox" name="safeoid_toc_headings[]"
                                value="<?php echo esc_attr($h); ?>"
                                <?php checked(in_array($h, $headings)); ?>>
                            <?php echo esc_html(strtoupper($h)); ?>
                        </label><br>
                    <?php endforeach; ?>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="toc_label"><?php _e('TOC Label', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="text" id="toc_label" name="toc_label" value="<?php echo esc_attr($toc_label); ?>" class="regular-text">
                    <p class="description"><?php _e('The heading text displayed above the Table of Contents.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label><?php _e('Display Options', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text"><?php _e('Choose where to display the TOC', SAFEOID_TOC_TEXT_DOMAIN); ?></legend>
                        <label>
                            <input type="checkbox" name="enable_desktop" value="1" <?php checked($enable_desktop); ?>>
                            <?php _e('Enable floating TOC on desktop', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label><br>
                        <label>
                            <input type="checkbox" name="enable_mobile" value="1" <?php checked($enable_mobile); ?>>
                            <?php _e('Enable sticky TOC on mobile', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label>
                        <p class="description"><?php _e('Choose where the TOC should be displayed. Desktop shows a floating sidebar, mobile shows a collapsible header.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                    </fieldset>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="text_color"><?php _e('Text Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="text_color" name="text_color" value="<?php echo esc_attr($text_color); ?>">
                    <p class="description"><?php _e('Color for the TOC text and headings.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="link_color"><?php _e('Link Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="link_color" name="link_color" value="<?php echo esc_attr($link_color); ?>">
                    <p class="description"><?php _e('Color for the TOC links.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="background_color"><?php _e('Background Color', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <input type="color" id="background_color" name="background_color" value="<?php echo esc_attr($background_color); ?>">
                    <p class="description"><?php _e('Background color for the TOC container.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label><?php _e('SEO Options', SAFEOID_TOC_TEXT_DOMAIN); ?></label>
                </th>
                <td>
                    <fieldset>
                        <legend class="screen-reader-text"><?php _e('SEO and Schema options', SAFEOID_TOC_TEXT_DOMAIN); ?></legend>
                        <label>
                            <input type="checkbox" name="disable_schema" value="1" <?php checked($disable_schema); ?>>
                            <?php _e('Disable schema markup output', SAFEOID_TOC_TEXT_DOMAIN); ?>
                        </label>
                        <p class="description"><?php _e('By default, the plugin outputs TableOfContents schema markup for SEO. Check this to disable it if you have conflicts with other SEO plugins.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
                    </fieldset>
                </td>
            </tr>
        </table>
        
        <?php submit_button(); ?>
    </form>
    
    <div class="safeoid-toc-info" style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-left: 4px solid #0073aa;">
        <h3><?php _e('Plugin Information', SAFEOID_TOC_TEXT_DOMAIN); ?></h3>
        <p><strong><?php _e('Version:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <?php echo SAFEOID_TOC_VERSION; ?></p>
        <p><strong><?php _e('Author:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <a href="https://safeoid.com" target="_blank">Safeoid</a></p>
        <p><strong><?php _e('Plugin URI:', SAFEOID_TOC_TEXT_DOMAIN); ?></strong> <a href="https://safeoid.com/floating-toc" target="_blank">https://safeoid.com/floating-toc</a></p>
        
        <h4><?php _e('Features:', SAFEOID_TOC_TEXT_DOMAIN); ?></h4>
        <ul>
            <li><?php _e('✅ Responsive design - floating sidebar on desktop, sticky header on mobile', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ No jQuery dependency - pure vanilla JavaScript', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ No database writes - dynamically generated', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ SEO-friendly with schema markup', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ Elementor widget included', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
            <li><?php _e('✅ Compatible with caching plugins', SAFEOID_TOC_TEXT_DOMAIN); ?></li>
        </ul>
        
        <h4><?php _e('Usage:', SAFEOID_TOC_TEXT_DOMAIN); ?></h4>
        <p><?php _e('The TOC will automatically appear on posts and pages that contain the selected heading levels. For Elementor users, you can also drag the "Floating TOC" widget onto any page.', SAFEOID_TOC_TEXT_DOMAIN); ?></p>
    </div>
</div>

<style>
.safeoid-toc-info ul {
    list-style-type: none;
    padding-left: 0;
}

.safeoid-toc-info ul li {
    margin-bottom: 5px;
}
</style>

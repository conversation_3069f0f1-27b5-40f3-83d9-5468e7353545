/**
 * Safeoid TOC JavaScript
 * Simple vanilla JS for TOC functionality
 */

(function() {
    'use strict';

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTOC);
    } else {
        initTOC();
    }

    function initTOC() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Initialize smooth scrolling
        initSmoothScrolling();

        // Initialize scroll-based collapse behavior
        initScrollCollapse();

        // Initialize click to expand functionality
        initClickToExpand();
    }

    /**
     * Initialize smooth scrolling for TOC links
     */
    function initSmoothScrolling() {
        const tocLinks = document.querySelectorAll('.floating-toc a[href^="#"]');
        console.log('Found TOC links:', tocLinks.length);

        tocLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                console.log('Clicked link to:', targetId);
                console.log('Target element found:', !!targetElement);

                if (targetElement) {
                    const offsetTop = getElementOffsetTop(targetElement);
                    const scrollOffset = getScrollOffset();

                    console.log('Scrolling to:', offsetTop - scrollOffset);

                    // Try smooth scrolling, fallback to instant
                    try {
                        window.scrollTo({
                            top: offsetTop - scrollOffset,
                            behavior: 'smooth'
                        });
                    } catch (e) {
                        // Fallback for older browsers
                        window.scrollTo(0, offsetTop - scrollOffset);
                    }

                    // Close mobile TOC after clicking
                    if (window.innerWidth <= 1024) {
                        document.body.classList.remove('toc-open');
                    }
                } else {
                    console.error('Target element not found for ID:', targetId);
                }
            });
        });
    }

    /**
     * Get element's offset from top of page
     */
    function getElementOffsetTop(element) {
        let offsetTop = 0;
        while (element) {
            offsetTop += element.offsetTop;
            element = element.offsetParent;
        }
        return offsetTop;
    }

    /**
     * Get scroll offset for fixed headers
     */
    function getScrollOffset() {
        let offset = 20; // Default offset

        // WordPress admin bar
        const adminBar = document.getElementById('wpadminbar');
        if (adminBar) {
            offset += adminBar.offsetHeight;
        }

        // Mobile TOC
        if (window.innerWidth <= 1024) {
            offset += 60; // Mobile TOC height
        }

        // Check for common theme headers
        const stickyHeaders = document.querySelectorAll('.sticky-header, .fixed-header, header.fixed');
        stickyHeaders.forEach(function(header) {
            if (window.getComputedStyle(header).position === 'fixed') {
                offset += header.offsetHeight;
            }
        });

        return offset;
    }

    /**
     * Initialize scroll-based collapse behavior
     */
    function initScrollCollapse() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        let lastScrollTop = 0;
        let scrollThreshold = 100; // Pixels to scroll before collapsing
        let isCollapsed = false;

        function handleScroll() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Only collapse on desktop (not mobile)
            if (window.innerWidth > 1024) {
                if (scrollTop > scrollThreshold && !isCollapsed) {
                    // Collapse the TOC
                    tocElement.classList.add('collapsed');
                    isCollapsed = true;
                } else if (scrollTop <= scrollThreshold && isCollapsed) {
                    // Expand the TOC
                    tocElement.classList.remove('collapsed');
                    isCollapsed = false;
                }
            }

            lastScrollTop = scrollTop;
        }

        // Throttle scroll events for better performance
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', onScroll);
    }

    /**
     * Initialize click to expand functionality
     */
    function initClickToExpand() {
        const tocElement = document.querySelector('.floating-toc');
        if (!tocElement) return;

        // Handle click on collapsed TOC to expand
        tocElement.addEventListener('click', function(e) {
            if (tocElement.classList.contains('collapsed')) {
                e.preventDefault();
                tocElement.classList.remove('collapsed');

                // Prevent immediate re-collapse by temporarily increasing scroll threshold
                setTimeout(function() {
                    // Reset scroll threshold after a short delay
                }, 1000);
            }
        });

        // Handle click on toggle button for mobile
        const toggleButton = tocElement.querySelector('.toc-toggle');
        if (toggleButton) {
            toggleButton.addEventListener('click', function(e) {
                e.stopPropagation();
                document.body.classList.toggle('toc-open');
            });
        }
    }
    
    /**
     * Future feature: Scroll spy to highlight current section
     * Uncomment and customize as needed
     */
    /*
    function initScrollSpy() {
        const tocLinks = document.querySelectorAll('.safeoid-toc a[href^="#"]');
        const headings = [];
        
        tocLinks.forEach(function(link) {
            const targetId = link.getAttribute('href').substring(1);
            const heading = document.getElementById(targetId);
            if (heading) {
                headings.push({
                    element: heading,
                    link: link,
                    top: getElementOffsetTop(heading)
                });
            }
        });
        
        if (headings.length === 0) return;
        
        function updateActiveLink() {
            const scrollTop = window.pageYOffset;
            const scrollOffset = getScrollOffset();
            
            let activeHeading = headings[0];
            
            for (let i = 0; i < headings.length; i++) {
                if (scrollTop >= headings[i].top - scrollOffset - 50) {
                    activeHeading = headings[i];
                }
            }
            
            // Remove active class from all links
            tocLinks.forEach(function(link) {
                link.classList.remove('active');
            });
            
            // Add active class to current link
            if (activeHeading) {
                activeHeading.link.classList.add('active');
            }
        }
        
        // Throttle scroll events
        let ticking = false;
        function onScroll() {
            if (!ticking) {
                requestAnimationFrame(function() {
                    updateActiveLink();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', onScroll);
        updateActiveLink(); // Initial call
    }
    */
    
})();

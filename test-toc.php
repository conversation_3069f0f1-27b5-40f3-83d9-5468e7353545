<?php
/**
 * Simple test file to verify TOC generation
 * This file can be used to test the TOC functionality outside of WordPress
 */

// Test content with headings
$test_content = '
<h1>Main Title</h1>
<p>This is some content before the first heading.</p>

<h2>Introduction</h2>
<p>This is the introduction section with some content.</p>

<h3>Getting Started</h3>
<p>This subsection explains how to get started.</p>

<h2>Benefits</h2>
<p>Here are the main benefits of using this system.</p>

<h3>Performance Benefits</h3>
<p>Performance improvements you can expect.</p>

<h3>User Experience Benefits</h3>
<p>How the user experience is improved.</p>

<h2>Features</h2>
<p>Main features of the system.</p>

<h3>Core Features</h3>
<p>The essential features everyone needs.</p>

<h4>Basic Functionality</h4>
<p>Basic functions available to all users.</p>

<h4>Advanced Options</h4>
<p>Advanced options for power users.</p>

<h3>Premium Features</h3>
<p>Additional features available in the premium version.</p>

<h2>Examples</h2>
<p>Real-world examples of how to use the system.</p>

<h2>FAQs</h2>
<p>Frequently asked questions and their answers.</p>

<h2>Conclusion</h2>
<p>Final thoughts and summary.</p>
';

// Test the regex pattern
$headings = array('h2', 'h3', 'h4');
$pattern = '/<(' . implode('|', array_map('preg_quote', $headings)) . ')([^>]*)>(.*?)<\/\1>/is';
preg_match_all($pattern, $test_content, $matches, PREG_SET_ORDER);

echo "<h1>TOC Test Results</h1>\n";
echo "<h2>Found " . count($matches) . " headings:</h2>\n";
echo "<ul>\n";

foreach ($matches as $i => $match) {
    $tag = strtolower($match[1]);
    $attrs = $match[2];
    $title = strip_tags($match[3]);
    
    echo "<li><strong>$tag:</strong> " . htmlspecialchars($title) . "</li>\n";
}

echo "</ul>\n";

// Test anchor generation
echo "<h2>Generated Anchors:</h2>\n";
echo "<ul>\n";

$used_anchors = array();
foreach ($matches as $i => $match) {
    $title = strip_tags($match[3]);
    $anchor = sanitize_title_with_dashes($title) ?: 'toc-section-' . $i;
    
    $base_anchor = $anchor;
    $j = 1;
    while (in_array($anchor, $used_anchors)) {
        $anchor = $base_anchor . '-' . $j++;
    }
    $used_anchors[] = $anchor;
    
    echo "<li>" . htmlspecialchars($title) . " → <code>#$anchor</code></li>\n";
}

echo "</ul>\n";

// Simple sanitize function for testing
function sanitize_title_with_dashes($title) {
    $title = strtolower(trim($title));
    $title = preg_replace('/[^a-z0-9\-_]/', '', str_replace(' ', '-', $title));
    $title = preg_replace('/-+/', '-', $title);
    return trim($title, '-');
}
?>

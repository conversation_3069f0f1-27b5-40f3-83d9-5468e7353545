<?php
/**
 * Elementor TOC Widget
 * Custom Elementor widget for displaying the Table of Contents
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Safeoid TOC Elementor Widget
 */
class SafeoidTOCElementorWidget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     */
    public function get_name() {
        return 'safeoid_toc';
    }
    
    /**
     * Get widget title
     */
    public function get_title() {
        return __('Floating TOC', SAFEOID_TOC_TEXT_DOMAIN);
    }
    
    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-table-of-contents';
    }
    
    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['general'];
    }
    
    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['toc', 'table of contents', 'navigation', 'safeoid'];
    }
    
    /**
     * Register widget controls
     */
    protected function _register_controls() {
        $this->start_controls_section(
            'section_content',
            [
                'label' => __('TOC Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );
        
        $this->add_control(
            'toc_title',
            [
                'label' => __('TOC Title', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => 'Table of Contents',
                'placeholder' => __('Enter your title', SAFEOID_TOC_TEXT_DOMAIN),
            ]
        );
        
        $this->add_control(
            'headings',
            [
                'label' => __('Include Headings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => [
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                ],
                'default' => ['h2', 'h3', 'h4'],
            ]
        );
        
        $this->add_control(
            'use_global_settings',
            [
                'label' => __('Use Global Settings', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', SAFEOID_TOC_TEXT_DOMAIN),
                'label_off' => __('No', SAFEOID_TOC_TEXT_DOMAIN),
                'return_value' => 'yes',
                'default' => '', // Disabled by default
            ]
        );

        $this->add_control(
            'display_options_heading',
            [
                'label' => __('Display Options', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );

        $this->add_control(
            'enable_desktop',
            [
                'label' => __('Enable floating TOC on desktop', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', SAFEOID_TOC_TEXT_DOMAIN),
                'label_off' => __('No', SAFEOID_TOC_TEXT_DOMAIN),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );

        $this->add_control(
            'enable_mobile',
            [
                'label' => __('Enable sticky TOC on mobile', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', SAFEOID_TOC_TEXT_DOMAIN),
                'label_off' => __('No', SAFEOID_TOC_TEXT_DOMAIN),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );

        $this->end_controls_section();
        
        $this->start_controls_section(
            'section_style',
            [
                'label' => __('TOC Style', SAFEOID_TOC_TEXT_DOMAIN),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'use_global_settings' => '',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'heading_typography',
                'label' => __('Heading Typography', SAFEOID_TOC_TEXT_DOMAIN),
                'selector' => '{{WRAPPER}} .floating-toc .toc-heading',
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'links_typography',
                'label' => __('Links Typography', SAFEOID_TOC_TEXT_DOMAIN),
                'selector' => '{{WRAPPER}} .floating-toc a',
            ]
        );

        $this->add_control(
            'text_color',
            [
                'label' => __('Text Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .floating-toc .toc-heading' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'link_color',
            [
                'label' => __('Link Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
                'selectors' => [
                    '{{WRAPPER}} .floating-toc a' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'link_hover_color',
            [
                'label' => __('Link Hover Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#005bb5',
                'selectors' => [
                    '{{WRAPPER}} .floating-toc a:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'background_color',
            [
                'label' => __('Background Color', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .floating-toc' => 'background-color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'border',
                'selector' => '{{WRAPPER}} .safeoid-toc',
                'separator' => 'before',
            ]
        );
        
        $this->add_control(
            'border_radius',
            [
                'label' => __('Border Radius', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'box_shadow',
                'selector' => '{{WRAPPER}} .safeoid-toc',
            ]
        );
        
        $this->add_responsive_control(
            'padding',
            [
                'label' => __('Padding', SAFEOID_TOC_TEXT_DOMAIN),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .safeoid-toc' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '20',
                    'right' => '20',
                    'bottom' => '20',
                    'left' => '20',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );
        
        $this->end_controls_section();
    }
    
    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Get global settings if enabled
        $use_global = $settings['use_global_settings'] === 'yes';

        // Determine which settings to use
        if ($use_global) {
            $toc_title = get_option('safeoid_toc_toc_label', 'Table of Contents');
            $headings = get_option('safeoid_toc_headings', array('h2', 'h3', 'h4'));
            $enable_desktop = get_option('safeoid_toc_enable_desktop', true);
            $enable_mobile = get_option('safeoid_toc_enable_mobile', true);
        } else {
            $toc_title = $settings['toc_title'];
            $headings = $settings['headings'];
            $enable_desktop = $settings['enable_desktop'] === 'yes';
            $enable_mobile = $settings['enable_mobile'] === 'yes';
        }

        // Check if TOC should be displayed based on device
        $is_mobile = wp_is_mobile();
        if (($is_mobile && !$enable_mobile) || (!$is_mobile && !$enable_desktop)) {
            return; // Don't display TOC
        }

        // Get the main plugin instance
        $plugin = SafeoidTOC::get_instance();

        // Get current post content
        global $post;
        $content = $post ? apply_filters('the_content', $post->post_content) : '';

        if (empty($content)) {
            echo '<div class="floating-toc elementor-widget">';
            echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
            echo '<div class="toc-content">';
            echo '<p>' . __('No content found to generate TOC.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            echo '</div>';
            echo '</div>';
            return;
        }

        // Temporarily set headings for this widget
        $original_headings = get_option('safeoid_toc_headings');
        update_option('safeoid_toc_headings', $headings);

        // Generate TOC
        $toc_html = $plugin->build_toc($content);

        // Restore original headings
        update_option('safeoid_toc_headings', $original_headings);

        // Extract just the TOC part (remove the content)
        if (preg_match('/<div class="floating-toc".*?<\/div><\/div>/s', $toc_html, $matches)) {
            // Add elementor-widget class for proper styling
            $toc_output = str_replace('class="floating-toc"', 'class="floating-toc elementor-widget"', $matches[0]);
            echo $toc_output;
        } else {
            echo '<div class="floating-toc elementor-widget">';
            echo '<h3 class="toc-heading">' . esc_html($toc_title) . '</h3>';
            echo '<div class="toc-content">';
            echo '<p>' . __('No headings found in the content.', SAFEOID_TOC_TEXT_DOMAIN) . '</p>';
            echo '</div>';
            echo '</div>';
        }
    }
}
